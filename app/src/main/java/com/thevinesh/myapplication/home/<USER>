package com.thevinesh.myapplication.home

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.thevinesh.myapplication.data.repository.HomeRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class HomeVm @Inject constructor(
    private val homeRepository: HomeRepository
) : ViewModel() {
    private val _query = MutableStateFlow("")
    val query = _query.asStateFlow()

    fun onQueryChanged(value: String) {
        _query.value = value
    }

    fun onSearch(query: String) {
        viewModelScope.launch {
            val response = homeRepository.getFirstPageOfGoogle(query)
            Log.e("Vinesh", "onSearch: ${response.body()}")
        }
    }
}