package com.thevinesh.myapplication.home

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.thevinesh.myapplication.ui.theme.LocalAiSearchTheme

@Composable
fun SearchBar(
    modifier: Modifier = Modifier,
    query: String,
    onValueChanged: (String) -> Unit,
    onSearch: (String) -> Unit
) {
    TextField(
        modifier = modifier.fillMaxWidth(),
        value = query,
        onValueChange = onValueChanged,
        placeholder = { Text("Search..") },
        trailingIcon = {
            IconButton(onClick = { onSearch(query) }) {
                Icon(
                    imageVector = Icons.Default.Search,
                    contentDescription = "Search"
                )
            }
        }
    )
}

@Preview(showBackground = true)
@Composable
fun SearchBarPreview() {
    LocalAiSearchTheme {
        SearchBar(
            query = "",
            onValueChanged = {},
            onSearch = {}
        )
    }
}