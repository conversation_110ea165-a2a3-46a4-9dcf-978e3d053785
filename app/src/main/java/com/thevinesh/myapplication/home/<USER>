package com.thevinesh.myapplication.home

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import com.thevinesh.myapplication.ui.theme.LocalAiSearchTheme

@Composable
fun HomeContent(
    vm: HomeVm = hiltViewModel()
) {
    val query = vm.query.collectAsState()
    Scaffold(
        modifier = Modifier.fillMaxSize(),
        bottomBar = {
            SearchBar(
                query = query.value,
                onValueChanged = vm::onQueryChanged,
                onSearch = vm::onSearch
            )
        }
    ) { innerPadding ->
        Column(modifier = Modifier
            .fillMaxSize()
            .padding(innerPadding)) { }
    }
}

@Preview(showBackground = true)
@Composable
fun HomeContentPreview() {
    LocalAiSearchTheme {
        HomeContent()
    }
}