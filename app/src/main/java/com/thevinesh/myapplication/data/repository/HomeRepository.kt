package com.thevinesh.myapplication.data.repository

import com.thevinesh.myapplication.data.api.ApiService
import retrofit2.Response
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class HomeRepository @Inject constructor(
    private val apiService: ApiService
) {

    suspend fun getFirstPageOfGoogle(query: String): Response<String> {
        return apiService.getFirstPageOfGoogle(query)
    }
}
